import express from 'express';

/**
 * LM Studio API endpoints
 */

const router = express.Router();

/**
 * Test LM Studio connection and get server status
 */
router.post('/ping', async (request, response) => {
    try {
        const { url } = request.body;
        
        if (!url) {
            return response.status(400).json({ error: 'URL is required' });
        }

        // Validate URL format
        let serverUrl;
        try {
            serverUrl = new URL(url);
        } catch (error) {
            return response.status(400).json({ error: 'Invalid URL format' });
        }

        // Test connection to LM Studio server
        const healthUrl = new URL('/v1/models', serverUrl);
        
        const result = await fetch(healthUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: 10000, // 10 second timeout
        });

        if (!result.ok) {
            throw new Error(`LM Studio server returned ${result.status}: ${result.statusText}`);
        }

        const data = await result.json();
        
        // Check if models are available
        if (!data.data || !Array.isArray(data.data)) {
            throw new Error('Invalid response format from LM Studio server');
        }

        const modelCount = data.data.length;
        
        return response.json({
            status: 'connected',
            message: `Successfully connected to LM Studio server`,
            models_available: modelCount,
            server_url: url,
            models: data.data.map(model => ({
                id: model.id,
                object: model.object,
                created: model.created,
                owned_by: model.owned_by
            }))
        });

    } catch (error) {
        console.error('LM Studio ping error:', error);
        
        let errorMessage = 'Failed to connect to LM Studio server';
        let statusCode = 500;
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorMessage = 'Cannot reach LM Studio server. Please check if LM Studio is running and the server is started.';
            statusCode = 503;
        } else if (error.message.includes('timeout')) {
            errorMessage = 'Connection timeout. LM Studio server is not responding.';
            statusCode = 408;
        } else if (error.message.includes('ECONNREFUSED')) {
            errorMessage = 'Connection refused. Please check if LM Studio server is running on the specified port.';
            statusCode = 503;
        }
        
        return response.status(statusCode).json({
            status: 'error',
            error: errorMessage,
            details: error.message
        });
    }
});

/**
 * Get available models from LM Studio
 */
router.post('/models', async (request, response) => {
    try {
        const { url } = request.body;
        
        if (!url) {
            return response.status(400).json({ error: 'URL is required' });
        }

        const serverUrl = new URL(url);
        const modelsUrl = new URL('/v1/models', serverUrl);
        
        const result = await fetch(modelsUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!result.ok) {
            throw new Error(`LM Studio server returned ${result.status}: ${result.statusText}`);
        }

        const data = await result.json();
        return response.json(data);

    } catch (error) {
        console.error('LM Studio models error:', error);
        return response.status(500).json({
            error: 'Failed to fetch models from LM Studio server',
            details: error.message
        });
    }
});

/**
 * Get server information from LM Studio
 */
router.post('/info', async (request, response) => {
    try {
        const { url } = request.body;
        
        if (!url) {
            return response.status(400).json({ error: 'URL is required' });
        }

        const serverUrl = new URL(url);
        
        // Try to get server info (some LM Studio versions support this)
        const infoUrl = new URL('/v1/info', serverUrl);
        
        try {
            const result = await fetch(infoUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (result.ok) {
                const data = await result.json();
                return response.json(data);
            }
        } catch (infoError) {
            // If /v1/info is not available, fall back to models endpoint
            console.debug('LM Studio /v1/info not available, using models endpoint');
        }

        // Fallback: get basic info from models endpoint
        const modelsUrl = new URL('/v1/models', serverUrl);
        const result = await fetch(modelsUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!result.ok) {
            throw new Error(`LM Studio server returned ${result.status}: ${result.statusText}`);
        }

        const data = await result.json();
        
        return response.json({
            server: 'LM Studio',
            version: 'unknown',
            models_loaded: data.data ? data.data.length : 0,
            status: 'running'
        });

    } catch (error) {
        console.error('LM Studio info error:', error);
        return response.status(500).json({
            error: 'Failed to get server info from LM Studio',
            details: error.message
        });
    }
});

export { router };
