# SillyTavern LM Studio 默认配置更改

## 概述
本文档记录了将SillyTavern的默认运行模型设定为本地LM Studio的所有配置更改。

## 更改内容

### 1. 默认配置文件修改 (default/content/settings.json)

**修改的字段：**
- `main_api`: 从 `"koboldhorde"` 改为 `"openai"`
- `chat_completion_source`: 从 `"openai"` 改为 `"lmstudio"`
- 新增 `lmstudio_model`: `""`（空字符串，将自动选择第一个可用模型）
- 新增 `lmstudio_url`: `"http://localhost:1234/v1"`（LM Studio默认端点）

**目的：** 确保新用户首次启动时默认使用LM Studio作为聊天完成API。

### 2. OpenAI脚本默认设置修改 (public/scripts/openai.js)

**修改的字段：**
- `default_settings.chat_completion_source`: 从 `chat_completion_sources.OPENAI` 改为 `chat_completion_sources.LMSTUDIO`
- 在 `getChatCompletionModel` 函数中添加了对 `chat_completion_sources.LMSTUDIO` 的支持

**目的：** 确保JavaScript代码中的默认设置也指向LM Studio。

### 3. 主脚本默认API设置修改 (public/script.js)

**修改的字段：**
- 在设置加载逻辑中，将未定义时的默认 `main_api` 从 `'kobold'` 改为 `'openai'`

**目的：** 确保首次运行时选择OpenAI API类型（包含LM Studio选项）。

### 4. 创建LM Studio专用预设 (default/content/presets/openai/LM Studio.json)

**新建文件内容：**
- `chat_completion_source`: `"lmstudio"`
- `lmstudio_url`: `"http://localhost:1234/v1"`
- `temperature`: `0.8`（适合对话的温度设置）
- `top_p`: `0.9`
- `top_k`: `40`
- `min_p`: `0.05`
- `repetition_penalty`: `1.1`
- `openai_max_context`: `8192`（较大的上下文窗口）
- `openai_max_tokens`: `512`（适中的响应长度）

**目的：** 为LM Studio提供优化的默认参数配置。

## 配置说明

### LM Studio 连接设置
- **默认端点**: `http://localhost:1234/v1`
- **模型选择**: 自动检测并选择LM Studio中加载的第一个可用模型
- **API兼容性**: 使用OpenAI兼容的API格式

### 推荐的LM Studio设置
1. 启动LM Studio
2. 在"Local Server"标签页中加载一个聊天模型
3. 启动本地服务器（默认端口1234）
4. SillyTavern将自动连接并检测可用模型

### 参数优化
- **温度 (0.8)**: 平衡创造性和一致性
- **Top-p (0.9)**: 核采样，保持响应质量
- **重复惩罚 (1.1)**: 减少重复内容
- **上下文长度 (8192)**: 支持较长的对话历史

## 验证步骤

1. **检查默认配置**: 确认 `default/content/settings.json` 中的设置正确
2. **验证JavaScript设置**: 确认 `public/scripts/openai.js` 中的默认值已更新
3. **测试首次启动**: 新安装应该默认选择LM Studio
4. **检查预设文件**: 确认LM Studio预设文件存在且配置合理

## 用户体验改进

### 首次使用流程
1. 用户启动SillyTavern
2. 系统自动选择OpenAI API类型
3. 聊天完成源默认设置为LM Studio
4. 用户只需在LM Studio中加载模型并启动服务器
5. SillyTavern自动检测并连接到本地模型

### API配置界面
- LM Studio选项在聊天完成源下拉菜单中可见
- 提供专用的端点URL配置字段
- 自动模型检测和选择功能
- 中文界面支持（通过现有的本地化系统）

## 技术细节

### 代码更改位置
1. `default/content/settings.json` - 第9行、第628-631行
2. `public/scripts/openai.js` - 第391行、第478行、第1627-1628行
3. `public/script.js` - 第6921行
4. `default/content/presets/openai/LM Studio.json` - 新建文件

### 兼容性
- 保持与现有API的完全兼容性
- 不影响已有用户的配置
- 支持在不同API之间切换
- 保留所有现有功能

## 注意事项

1. **LM Studio要求**: 用户需要单独安装和配置LM Studio
2. **模型依赖**: 需要在LM Studio中加载兼容的聊天模型
3. **网络配置**: 确保本地端口1234可访问
4. **性能考虑**: 本地模型性能取决于硬件配置

## 后续建议

1. 在用户文档中添加LM Studio设置指南
2. 考虑添加LM Studio连接状态检测
3. 提供模型推荐列表
4. 添加性能优化建议
